const https = require('https');
const { URL } = require('url');
const fs = require('fs');
const FormData = require('form-data');

class EtrayGeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({
        apiKey: process.env.GEMINI_API_KEY,
        httpOptions: {
          timeout: 600000
        }
      });
    }
    return this.client;
  }

  async uploadFileToGemini(filePath, mimeType, displayName) {
    return new Promise((resolve, reject) => {
      try {
        const form = new FormData();
        form.append('file', fs.createReadStream(filePath));
        
        const metadata = {
          file: {
            display_name: displayName
          }
        };
        form.append('metadata', JSON.stringify(metadata), {
          contentType: mimeType
        });

        const requestUrl = `https://generativelanguage.googleapis.com/upload/v1beta/files?key=${process.env.GEMINI_API_KEY}`;
        const url = new URL(requestUrl);

        const options = {
          hostname: url.hostname,
          port: url.port || 443,
          path: url.pathname + url.search,
          method: 'POST',
          headers: {
            ...form.getHeaders(),
          },
          timeout: 300000 // 5 minutes timeout for file upload
        };

        const req = https.request(options, (res) => {
          let data = '';
          res.on('data', (chunk) => { data += chunk; });

          res.on('end', () => {
            try {
              if (res.statusCode !== 200) {
                console.error('Upload error response:', data);
                reject(new Error(`HTTP error! status: ${res.statusCode}, response: ${data}`));
                return;
              }

              const responseData = JSON.parse(data);
              console.log('File uploaded successfully:', responseData.file.name);
              resolve(responseData.file);
            } catch (parseError) {
              console.error('Error parsing upload response:', parseError);
              reject(new Error('Failed to parse upload response from Gemini'));
            }
          });
        });

        req.on('error', (error) => {
          console.error('Upload request error:', error);
          reject(new Error('Failed to upload file to Gemini'));
        });

        req.on('timeout', () => {
          req.destroy();
          reject(new Error('Upload timeout - Gemini API took too long to respond'));
        });

        req.setTimeout(300000); // 5 minutes timeout
        form.pipe(req);

      } catch (error) {
        console.error('Error uploading file:', error);
        reject(new Error('Failed to upload file to Gemini'));
      }
    });
  }

  async generateResponseWithFiles(model, prompt, config, fileUris = []) {
    return new Promise((resolve, reject) => {
      try {
        const requestUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;
        const queryParams = `key=${process.env.GEMINI_API_KEY}`;
        const url = new URL(`${requestUrl}?${queryParams}`);

        // Build content parts - text prompt and file references
        const contentParts = [{ text: prompt }];
        
        // Add file references
        fileUris.forEach(fileObject => {
          const fileUri = fileObject.gemini_uri;
          const fileType = fileObject.mime_type;
          contentParts.push({
            file_data: {
              mime_type: fileType, // Default to PDF, can be made dynamic
              file_uri: fileUri
            }
          });
        });

        const postData = JSON.stringify({
          system_instruction: { parts: [{ text: config.systemInstruction }] },
          contents: [{ parts: contentParts }],
          generationConfig: {
            temperature: config.temperature,
            responseMimeType: config.responseMimeType
          }
        });

        // console.log('postData:', postData);

        const options = {
          hostname: url.hostname,
          port: url.port || 443,
          path: url.pathname + url.search,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
          },
          timeout: 600000 // 10 minutes timeout
        };

        const req = https.request(options, (res) => {
          let data = '';
          res.on('data', (chunk) => { data += chunk; });

          res.on('end', () => {
            try {
              if (res.statusCode !== 200) {
                console.error('Generation error response:', data);
                reject(new Error(`HTTP error! status: ${res.statusCode}, response: ${data}`));
                return;
              }

              const responseData = JSON.parse(data);
              resolve(responseData.candidates[0].content.parts[0].text);
            } catch (parseError) {
              console.error('Error parsing response:', parseError);
              reject(new Error('Failed to parse response from Gemini'));
            }
          });
        });

        req.on('error', (error) => {
          console.error('Request error:', error);
          reject(new Error('Failed to generate response from Gemini'));
        });

        req.on('timeout', () => {
          req.destroy();
          reject(new Error('Request timeout - Gemini API took too long to respond'));
        });

        req.setTimeout(600000); // 10 minutes timeout
        req.write(postData);
        req.end();

      } catch (error) {
        console.error('Error generating response:', error);
        reject(new Error('Failed to generate response from Gemini'));
      }
    });
  }

  async generateResponse(model, prompt, config) {
    return this.generateResponseWithFiles(model, prompt, config, []);
  }

  async runEtrayPromptChain(input, analysisPrompt, formattingPrompt) {
    try {
      await this.initPromise; // Ensure client is initialized

      const { transcript, competencies, workingResultFile, answerKeyFile } = input;

      // Upload files to Gemini if provided
      let workingResultUri = {};
      let answerKeyUri = {};

      if (workingResultFile && workingResultFile.path) {
        console.log('Uploading working result file to Gemini...');
        const workingResultUpload = await this.uploadFileToGemini(
          workingResultFile.path,
          workingResultFile.mimetype,
          'Working Result Document'
        );
        workingResultUri = {
          mime_type: workingResultFile.mimeType,
          gemini_uri: workingResultUpload.uri
        };
      }

      if (answerKeyFile && answerKeyFile.path) {
        console.log('Uploading answer key file to Gemini...');
        const answerKeyUpload = await this.uploadFileToGemini(
          answerKeyFile.path,
          answerKeyFile.mimetype,
          'Answer Key Document'
        );
        answerKeyUri = {
          mime_type: answerKeyFile.mimeType,
          gemini_uri: answerKeyUpload.uri
        };
      }

      // Step 1: Run E-tray analysis prompt with transcript, competencies, and files
      const analysisSystemPrompt = analysisPrompt.replace(
        '{{ assessment_guidelines }}',
        competencies
      );

      const analysisUserPrompt = `
        Here is the E-tray simulation data to analyze:
        
        Transcript: ${transcript}
        
        ${workingResultFile ? 'Working Result Document is attached as a file.' : 'No working result document provided.'}
        ${answerKeyFile ? 'Answer Key Document is attached as a file.' : 'No answer key document provided.'}
        
        Please analyze the participant's performance based on the provided documents and assessment guidelines.
      `;

      const analysisConfig = {
        temperature: 0.2,
        responseMimeType: "text/plain",
        systemInstruction: analysisSystemPrompt
      };

      console.log(`Current time: ${new Date().toISOString()}`);
      console.log('Running E-tray analysis prompt...');

      // Collect file URIs for analysis
      const fileUris = [];
      if (workingResultUri) fileUris.push(workingResultUri);
      if (answerKeyUri) fileUris.push(answerKeyUri);

      console.log('File URIs:', fileUris);
      // log all files
      console.log('Working Result File:', workingResultUri);
      console.log('Answer Key File:', answerKeyUri);

      const analysisOutput = await this.generateResponseWithFiles(
        'gemini-2.5-pro',
        analysisUserPrompt,
        analysisConfig,
        fileUris
      );

      console.log(`Current time: ${new Date().toISOString()}`);
      console.log('E-tray analysis completed. Starting formatting...');

      // Step 2: Run E-tray formatting prompt to convert analysis to structured format
      const formattingUserPrompt = `
        Please convert the following E-tray analysis to structured JSON format:

        ${analysisOutput}
      `;

      const formattingConfig = {
        temperature: 0.1,
        responseMimeType: "application/json",
        systemInstruction: formattingPrompt
      };

      const formattingOutput = await this.generateResponse(
        'gemini-2.5-flash',
        formattingUserPrompt,
        formattingConfig
      );

      console.log(`Current time: ${new Date().toISOString()}`);
      console.log('E-tray formatting completed.');

      return {
        analysis: {
          prompt: analysisUserPrompt,
          systemInstruction: analysisSystemPrompt,
          output: analysisOutput,
          fileUris: fileUris
        },
        formatting: {
          prompt: formattingUserPrompt,
          systemInstruction: formattingPrompt,
          output: formattingOutput
        },
        finalOutput: formattingOutput
      };
    } catch (error) {
      console.error('Error in E-tray prompt chain:', error);
      throw error;
    }
  }
}

module.exports = new EtrayGeminiService();
